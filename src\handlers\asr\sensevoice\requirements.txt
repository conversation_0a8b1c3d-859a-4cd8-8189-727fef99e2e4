# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o requirements.txt
aliyun-python-sdk-core==2.16.0
    # via
    #   aliyun-python-sdk-kms
    #   oss2
aliyun-python-sdk-kms==2.16.5
    # via oss2
antlr4-python3-runtime==4.9.3
    # via
    #   hydra-core
    #   omegaconf
audioread==3.0.1
    # via librosa
certifi==2025.1.31
    # via requests
cffi==1.17.1
    # via
    #   cryptography
    #   soundfile
charset-normalizer==3.4.1
    # via requests
crcmod==1.7
    # via oss2
cryptography==44.0.2
    # via aliyun-python-sdk-core
decorator==5.2.1
    # via librosa
editdistance==0.8.1
    # via funasr
funasr==1.2.6
    # via sensevoice (pyproject.toml)
hydra-core==1.3.2
    # via funasr
idna==3.10
    # via requests
jaconv==0.4.0
    # via funasr
jamo==0.4.1
    # via funasr
jieba==0.42.1
    # via funasr
jmespath==0.10.0
    # via aliyun-python-sdk-core
joblib==1.4.2
    # via
    #   librosa
    #   pynndescent
    #   scikit-learn
kaldiio==2.18.1
    # via funasr
lazy-loader==0.4
    # via librosa
librosa==0.11.0
    # via funasr
llvmlite==0.44.0
    # via
    #   numba
    #   pynndescent
modelscope==1.25.0
    # via funasr
msgpack==1.1.0
    # via librosa
numba==0.61.2
    # via
    #   librosa
    #   pynndescent
    #   umap-learn
numpy==2.2.4
    # via
    #   kaldiio
    #   librosa
    #   numba
    #   pytorch-wpe
    #   scikit-learn
    #   scipy
    #   soundfile
    #   soxr
    #   tensorboardx
    #   torch-complex
    #   umap-learn
omegaconf==2.3.0
    # via hydra-core
oss2==2.19.1
    # via funasr
packaging==24.2
    # via
    #   hydra-core
    #   lazy-loader
    #   pooch
    #   tensorboardx
    #   torch-complex
platformdirs==4.3.7
    # via pooch
pooch==1.8.2
    # via librosa
protobuf==6.30.2
    # via tensorboardx
pycparser==2.22
    # via cffi
pycryptodome==3.22.0
    # via oss2
pynndescent==0.5.13
    # via umap-learn
pytorch-wpe==0.0.1
    # via funasr
pyyaml==6.0.2
    # via
    #   funasr
    #   omegaconf
requests==2.32.3
    # via
    #   funasr
    #   modelscope
    #   oss2
    #   pooch
scikit-learn==1.6.1
    # via
    #   librosa
    #   pynndescent
    #   umap-learn
scipy==1.15.2
    # via
    #   funasr
    #   librosa
    #   pynndescent
    #   scikit-learn
    #   umap-learn
sentencepiece==0.2.0
    # via funasr
six==1.17.0
    # via oss2
soundfile==0.13.1
    # via
    #   funasr
    #   librosa
soxr==0.5.0.post1
    # via librosa
tensorboardx==*******
    # via funasr
threadpoolctl==3.6.0
    # via scikit-learn
torch-complex==0.4.4
    # via funasr
tqdm==4.67.1
    # via
    #   funasr
    #   modelscope
    #   umap-learn
typing-extensions==4.13.2
    # via librosa
umap-learn==0.5.7
    # via funasr
urllib3==2.4.0
    # via
    #   modelscope
    #   requests
