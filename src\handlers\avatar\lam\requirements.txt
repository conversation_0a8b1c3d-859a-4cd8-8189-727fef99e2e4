# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o requirements.txt
asttokens==3.0.0
    # via stack-data
certifi==2025.1.31
    # via requests
charset-normalizer==3.4.1
    # via requests
contourpy==1.3.1
    # via matplotlib
cycler==0.12.1
    # via matplotlib
decorator==5.2.1
    # via ipython
easydict==1.13
    # via lam (pyproject.toml)
exceptiongroup==1.2.2
    # via ipython
executing==2.2.0
    # via stack-data
fasttext-wheel==0.9.2
    # via lam (pyproject.toml)
filelock==3.18.0
    # via
    #   huggingface-hub
    #   transformers
fonttools==4.57.0
    # via matplotlib
fsspec==2025.3.2
    # via huggingface-hub
huggingface-hub==0.30.2
    # via
    #   tokenizers
    #   transformers
idna==3.10
    # via requests
ipython==8.20.0
    # via lam (pyproject.toml)
jedi==0.19.2
    # via ipython
kiwisolver==1.4.8
    # via matplotlib
lmdb==1.4.1
    # via lam (pyproject.toml)
matplotlib==3.10.1
    # via lam (pyproject.toml)
matplotlib-inline==0.1.7
    # via ipython
numpy==1.26.4
    # via
    #   contourpy
    #   fasttext-wheel
    #   matplotlib
    #   pykalman
    #   scipy
    #   transformers
packaging==24.2
    # via
    #   huggingface-hub
    #   matplotlib
    #   transformers
parso==0.8.4
    # via jedi
pexpect==4.9.0
    # via ipython
pillow==11.2.1
    # via matplotlib
prompt-toolkit==3.0.50
    # via ipython
ptyprocess==0.7.0
    # via pexpect
pure-eval==0.2.3
    # via stack-data
pyarrow==19.0.1
    # via lam (pyproject.toml)
pybind11==2.13.6
    # via fasttext-wheel
pygments==2.19.1
    # via ipython
pykalman==0.9.7
    # via lam (pyproject.toml)
pyparsing==3.2.3
    # via matplotlib
python-dateutil==2.9.0.post0
    # via matplotlib
pyyaml==6.0.2
    # via
    #   huggingface-hub
    #   transformers
regex==2024.11.6
    # via transformers
requests==2.32.3
    # via
    #   huggingface-hub
    #   transformers
safetensors==0.5.3
    # via transformers
scipy==1.15.2
    # via pykalman
setuptools==78.1.0
    # via fasttext-wheel
six==1.17.0
    # via python-dateutil
stack-data==0.6.3
    # via ipython
tokenizers==0.19.1
    # via transformers
tqdm==4.67.1
    # via
    #   huggingface-hub
    #   transformers
traitlets==5.14.3
    # via
    #   ipython
    #   matplotlib-inline
transformers==4.44.1
    # via lam (pyproject.toml)
typing-extensions==4.13.2
    # via huggingface-hub
urllib3==2.4.0
    # via requests
wcwidth==0.2.13
    # via prompt-toolkit
